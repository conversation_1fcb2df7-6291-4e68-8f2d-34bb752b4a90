/**
 * 修复版行程详情页面
 * 逐步添加功能以确保路由正常工作
 */

import router from '@ohos.router';
import { Trip, TripDataManager, QuickAction, DailyItinerary } from '../models/TripModel';

// 路由参数接口
interface RouteParams {
  tripId: number;
}

@Entry
@Component
struct TripDetailPageFixed {
  @State trip: Trip | null = null;
  @State isLoading: boolean = true;
  @State quickActions: QuickAction[] = [];
  @State itineraries: DailyItinerary[] = [];
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('TripDetailPageFixed: aboutToAppear 开始');
    
    try {
      // 获取路由参数
      const params = router.getParams();
      console.log('TripDetailPageFixed: 路由参数:', JSON.stringify(params));

      if (params && typeof params === 'object') {
        const routeParams = params as RouteParams;
        if (routeParams.tripId !== undefined) {
          const tripId = routeParams.tripId;
          console.log(`TripDetailPageFixed: 获取行程ID: ${tripId}`);

          // 获取行程数据
          const foundTrip = this.tripManager.getTripById(tripId);
          this.trip = foundTrip || null;

          if (this.trip) {
            console.log(`TripDetailPageFixed: 成功找到行程: ${this.trip.title}`);
            // 加载快速操作和行程安排数据
            this.quickActions = this.tripManager.getQuickActions();
            this.itineraries = this.tripManager.getTripDetails(this.trip.id);
          } else {
            console.error(`TripDetailPageFixed: 未找到ID为 ${tripId} 的行程`);
          }
        }
      } else {
        console.error('TripDetailPageFixed: 未获取到有效的路由参数');
      }
    } catch (error) {
      console.error('TripDetailPageFixed: aboutToAppear 出错:', error);
    } finally {
      this.isLoading = false;
      console.log('TripDetailPageFixed: aboutToAppear 完成');
    }
  }

  // 返回按钮点击处理
  handleBackClick = () => {
    console.log('TripDetailPageFixed: 点击返回按钮');
    router.back();
  }

  // 构建行程信息卡片
  @Builder
  buildTripCard() {
    if (this.trip) {
      Column() {
        // 行程标题和编辑按钮
        Row() {
          Text(this.trip.title)
            .fontSize(20)
            .fontWeight(FontWeight.Bold)
            .fontColor(Color.White)
            .layoutWeight(1)

          // 编辑按钮
          Row() {
            Text('✏️')
              .fontSize(16)
              .fontColor(Color.White)
          }
          .width(32)
          .height(32)
          .backgroundColor('rgba(255, 255, 255, 0.2)')
          .borderRadius(6)
          .justifyContent(FlexAlign.Center)
          .alignItems(VerticalAlign.Center)
        }
        .width('100%')
        .margin({ bottom: 8 })

        // 目的地
        Text(this.trip.destination)
          .fontSize(14)
          .fontColor('rgba(255, 255, 255, 0.9)')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 16 })

        // 行程统计信息
        Row() {
          // 开始日期
          Column() {
            Text('开始日期')
              .fontSize(12)
              .fontColor('rgba(255, 255, 255, 0.8)')
              .margin({ bottom: 4 })
            Text(this.trip.startDate)
              .fontSize(14)
              .fontColor(Color.White)
              .fontWeight(FontWeight.Medium)
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          // 天数
          Column() {
            Text('天数')
              .fontSize(12)
              .fontColor('rgba(255, 255, 255, 0.8)')
              .margin({ bottom: 4 })
            Text(`${this.trip.daysCount}天`)
              .fontSize(14)
              .fontColor(Color.White)
              .fontWeight(FontWeight.Medium)
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          // 进度
          Column() {
            Text('进度')
              .fontSize(12)
              .fontColor('rgba(255, 255, 255, 0.8)')
              .margin({ bottom: 4 })
            Text(`${this.trip.progress}%`)
              .fontSize(14)
              .fontColor(Color.White)
              .fontWeight(FontWeight.Medium)
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)
        }
        .width('100%')
      }
      .width('100%')
      .padding(20)
      .backgroundColor('#4ECDC4')
      .borderRadius(12)
      .alignItems(HorizontalAlign.Start)
    }
  }

  // 构建快速操作区域
  @Builder
  buildQuickActions() {
    Column() {
      // 标题
      Row() {
        Text('快速操作')
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333333')
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 快速操作网格
      Column({ space: 12 }) {
        // 第一行
        Row({ space: 12 }) {
          this.buildQuickActionItem('🗓️', '查看日程', '#E8F8F5')
          this.buildQuickActionItem('🧳', '旅行物品', '#E8F8F5')
        }
        .width('100%')

        // 第二行
        Row({ space: 12 }) {
          this.buildQuickActionItem('💰', '预算管理', '#E8F8F5')
          this.buildQuickActionItem('🎒', '打包清单', '#E8F8F5')
        }
        .width('100%')

        // 第三行
        Row({ space: 12 }) {
          this.buildQuickActionItem('📋', '更新笔记', '#E8F8F5')
          this.buildQuickActionItem('➕', '添加活动', '#E8F8F5')
        }
        .width('100%')
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .alignItems(HorizontalAlign.Start)
  }

  // 构建单个快速操作项
  @Builder
  buildQuickActionItem(icon: string, title: string, bgColor: string) {
    Column() {
      Text(icon)
        .fontSize(24)
        .margin({ bottom: 8 })

      Text(title)
        .fontSize(12)
        .fontColor('#333333')
        .textAlign(TextAlign.Center)
    }
    .width('100%')
    .height(80)
    .backgroundColor(bgColor)
    .borderRadius(8)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .layoutWeight(1)
    .onClick(() => {
      console.log(`点击了快速操作: ${title}`);
    })
  }

  // 构建行程概览区域
  @Builder
  buildItineraryOverview() {
    Column() {
      // 标题和编辑按钮
      Row() {
        Text('行程概览')
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333333')

        Blank()

        Text('编辑')
          .fontSize(14)
          .fontColor('#4ECDC4')
          .onClick(() => {
            console.log('点击编辑行程概览');
          })
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 行程列表
      Column({ space: 16 }) {
        // 第一天
        this.buildDayItem(1, '埃菲尔铁塔', '7月16日 · 今天晚上')

        // 第二天
        this.buildDayItem(2, '经典景点', '7月17日 · 今天晚上')

        // 添加新一天按钮
        Row() {
          Text('➕')
            .fontSize(16)
            .fontColor('#4ECDC4')
            .margin({ right: 8 })

          Text('添加新一天')
            .fontSize(14)
            .fontColor('#4ECDC4')
        }
        .width('100%')
        .padding({ left: 16, top: 12, bottom: 12 })
        .onClick(() => {
          console.log('点击添加新一天');
        })
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .alignItems(HorizontalAlign.Start)
  }

  // 构建单日行程项
  @Builder
  buildDayItem(day: number, title: string, subtitle: string) {
    Row() {
      // 日期圆圈
      Text(day.toString())
        .fontSize(16)
        .fontColor(Color.White)
        .fontWeight(FontWeight.Bold)
        .width(32)
        .height(32)
        .backgroundColor('#4ECDC4')
        .borderRadius(16)
        .textAlign(TextAlign.Center)
        .margin({ right: 12 })

      // 内容
      Column() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text(subtitle)
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      // 更多按钮
      Text('⋯')
        .fontSize(20)
        .fontColor('#CCCCCC')
        .onClick(() => {
          console.log(`点击第${day}天更多选项`);
        })
    }
    .width('100%')
    .padding({ left: 0, right: 0, top: 8, bottom: 8 })
    .alignItems(VerticalAlign.Center)
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Row() {
          Text('←')
            .fontSize(20)
            .fontColor('#333333')
            .margin({ right: 8 })

          Text(this.trip?.title || '行程详情')
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .fontColor('#333333')
        }
        .onClick(this.handleBackClick)
        .padding({ left: 16, right: 16, top: 12, bottom: 12 })

        Blank()
      }
      .width('100%')
      .height(56)
      .backgroundColor(Color.White)
      .justifyContent(FlexAlign.Start)
      .alignItems(VerticalAlign.Center)
      
      // 内容区域
      if (this.isLoading) {
        // 加载状态
        Column() {
          Text('加载中...')
            .fontSize(16)
            .fontColor('#666666')
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
      } else if (this.trip) {
        // 行程内容
        Scroll() {
          Column({ space: 16 }) {
            // 行程信息卡片
            this.buildTripCard()

            // 快速操作区域
            this.buildQuickActions()

            // 行程概览区域
            this.buildItineraryOverview()
          }
          .padding(16)
        }
        .width('100%')
        .layoutWeight(1)
      } else {
        // 错误状态
        Column() {
          Text('未找到行程信息')
            .fontSize(16)
            .fontColor('#666666')
            .margin({ bottom: 20 })
          
          Button('返回主页')
            .onClick(this.handleBackClick)
            .backgroundColor('#007AFF')
            .fontColor(Color.White)
            .borderRadius(8)
            .padding({ left: 20, right: 20, top: 10, bottom: 10 })
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
