/**
 * 测试页面 - 用于验证路由是否正常工作
 */

import router from '@ohos.router';

@Entry
@Component
struct TestPage {
  @State message: string = '这是测试页面';

  build() {
    Column() {
      Text(this.message)
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      Text('如果你能看到这个页面，说明路由工作正常')
        .fontSize(16)
        .margin({ bottom: 30 })

      Button('返回主页')
        .onClick(() => {
          router.back();
        })
        .backgroundColor('#007AFF')
        .fontColor(Color.White)
        .borderRadius(8)
        .padding({ left: 20, right: 20, top: 10, bottom: 10 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor('#F5F5F5')
  }
}
