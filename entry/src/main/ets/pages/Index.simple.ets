// Simple version for testing
interface Trip {
  id: number;
  title: string;
  destination: string;
  startDate: string;
  endDate: string;
  daysCount: number;
  progress: number;
  status: string;
  tripType: string;
}

const SIMPLE_TRIPS: Trip[] = [
  {
    id: 1,
    title: '巴黎浪漫之旅',
    destination: '法国巴黎',
    startDate: '2024-07-15',
    endDate: '2024-07-22',
    daysCount: 8,
    progress: 75,
    status: 'upcoming',
    tripType: 'leisure'
  },
  {
    id: 2,
    title: '东京文化探索',
    destination: '日本东京',
    startDate: '2024-08-10',
    endDate: '2024-08-17',
    daysCount: 8,
    progress: 30,
    status: 'upcoming',
    tripType: 'leisure'
  },
  {
    id: 3,
    title: '上海商务会议',
    destination: '中国上海',
    startDate: '2024-06-26',
    endDate: '2024-06-30',
    daysCount: 5,
    progress: 60,
    status: 'in-progress',
    tripType: 'business'
  }
];

@Entry
@Component
struct Index {
  @State trips: Trip[] = SIMPLE_TRIPS;
  @State searchText: string = '';

  getStatusLabel(status: string): string {
    const labels: Record<string, string> = {
      'upcoming': '即将开始',
      'in-progress': '进行中',
      'completed': '已完成'
    };
    return labels[status] || '未知';
  }

  getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      'upcoming': '#007AFF',
      'in-progress': '#FF9500',
      'completed': '#34C759'
    };
    return colors[status] || '#8E8E93';
  }

  getTripTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      'business': '商务',
      'leisure': '休闲',
      'family': '家庭'
    };
    return labels[type] || '其他';
  }

  formatDateRange(startDate: string, endDate: string): string {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const startMonth = start.getMonth() + 1;
    const startDay = start.getDate();
    const endMonth = end.getMonth() + 1;
    const endDay = end.getDate();

    if (startMonth === endMonth) {
      return `${startMonth}月${startDay}-${endDay}日`;
    } else {
      return `${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;
    }
  }

  build() {
    Column() {
      // Header
      Text('我的行程')
        .fontSize(24)
        .fontWeight(700)
        .fontColor(Color.Black)
        .width('100%')
        .textAlign(TextAlign.Center)
        .margin({ top: 20, bottom: 20 })

      // Search Bar
      TextInput({ placeholder: '搜索行程...', text: this.searchText })
        .width('90%')
        .height(40)
        .backgroundColor('#f2f2f7')
        .borderRadius(12)
        .margin({ bottom: 20 })
        .onChange((value: string) => {
          this.searchText = value;
        })

      // Trip List
      List({ space: 12 }) {
        ForEach(this.trips, (trip: Trip) => {
          ListItem() {
            Column() {
              // Trip Header
              Row() {
                Column() {
                  Text(trip.title)
                    .fontSize(18)
                    .fontWeight(600)
                    .fontColor(Color.Black)
                    .alignSelf(ItemAlign.Start)

                  Text(trip.destination)
                    .fontSize(14)
                    .fontColor('#8e8e93')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)

                // Status Badge
                Text(this.getStatusLabel(trip.status))
                  .fontSize(12)
                  .fontWeight(500)
                  .fontColor(Color.White)
                  .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                  .backgroundColor(this.getStatusColor(trip.status))
                  .borderRadius(12)
              }
              .width('100%')
              .alignItems(VerticalAlign.Top)
              .margin({ bottom: 12 })

              // Date and Progress
              Text(`${this.formatDateRange(trip.startDate, trip.endDate)} • ${trip.daysCount}天`)
                .fontSize(14)
                .fontColor('#8e8e93')
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 8 })

              // Progress Bar
              Row() {
                Text('进度')
                  .fontSize(14)
                  .fontColor('#8e8e93')

                Blank()

                Text(`${trip.progress}%`)
                  .fontSize(14)
                  .fontWeight(600)
                  .fontColor(this.getStatusColor(trip.status))
              }
              .width('100%')
              .margin({ bottom: 6 })

              Progress({ value: trip.progress, total: 100, type: ProgressType.Linear })
                .width('100%')
                .height(4)
                .color(this.getStatusColor(trip.status))
                .backgroundColor('#f2f2f7')
            }
            .width('100%')
            .padding(16)
            .backgroundColor(Color.White)
            .borderRadius(12)
            .shadow({ radius: 2, color: 'rgba(0, 0, 0, 0.08)', offsetY: 1 })
          }
        })
      }
      .width('90%')
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f8f9fa')
    .padding({ left: 16, right: 16 })
  }
}
