/**
 * 简化版行程详情页面 - 用于测试路由
 */

import router from '@ohos.router';

// 路由参数接口
interface RouteParams {
  tripId: number;
}

@Entry
@Component
struct TripDetailPageSimple {
  @State tripId: number = 0;
  @State message: string = '行程详情页面';

  aboutToAppear() {
    console.log('TripDetailPageSimple: aboutToAppear 被调用');
    
    // 获取路由参数
    const params = router.getParams();
    console.log('TripDetailPageSimple: 路由参数:', JSON.stringify(params));

    if (params && typeof params === 'object') {
      const routeParams = params as RouteParams;
      if (routeParams.tripId !== undefined) {
        this.tripId = routeParams.tripId;
        this.message = `行程详情页面 - ID: ${this.tripId}`;
        console.log(`TripDetailPageSimple: 设置行程ID: ${this.tripId}`);
      }
    } else {
      console.error('TripDetailPageSimple: 未获取到有效的路由参数');
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('← 返回')
          .onClick(() => {
            console.log('点击返回按钮');
            router.back();
          })
          .backgroundColor(Color.Transparent)
          .fontColor('#007AFF')
          .fontSize(16)
        
        Blank()
        
        Text('行程详情')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
        
        Blank()
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Color.White)
      
      // 内容区域
      Column() {
        Text(this.message)
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })

        Text(`接收到的行程ID: ${this.tripId}`)
          .fontSize(16)
          .margin({ bottom: 20 })

        Text('如果你能看到这个页面，说明路由到详情页面工作正常')
          .fontSize(14)
          .fontColor('#666666')
          .textAlign(TextAlign.Center)
          .margin({ bottom: 30 })

        Button('返回主页')
          .onClick(() => {
            router.back();
          })
          .backgroundColor('#007AFF')
          .fontColor(Color.White)
          .borderRadius(8)
          .padding({ left: 20, right: 20, top: 10, bottom: 10 })
      }
      .width('100%')
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .padding(20)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
