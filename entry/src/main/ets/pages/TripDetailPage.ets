/**
 * 行程详情页面
 * 显示行程概览，包括顶部卡片、快速操作和行程安排
 */

import { Trip, DailyItinerary, QuickAction, Activity, TripDataManager } from '../models/TripModel';
import { QuickActionGrid } from '../components/QuickActionGrid';
import { ItineraryList } from '../components/ItineraryList';
import { 
  getStatusLabel, 
  getStatusColor, 
  getTripTypeLabel, 
  formatDateRange, 
  THEME_COLORS 
} from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

// 路由参数接口
interface RouteParams {
  tripId: number;
}

@Entry
@Component
struct TripDetailPage {
  @State trip: Trip | null = null;
  @State itineraries: DailyItinerary[] = [];
  @State quickActions: QuickAction[] = [];
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('TripDetailPage: aboutToAppear 被调用');

    // 获取路由参数
    const params = router.getParams();
    console.log('TripDetailPage: 路由参数:', JSON.stringify(params));

    if (params && typeof params === 'object') {
      const routeParams = params as RouteParams;
      if (routeParams.tripId !== undefined) {
        const tripId = routeParams.tripId;
        console.log(`TripDetailPage: 获取行程ID: ${tripId}`);
        const foundTrip = this.tripManager.getTripById(tripId);
        this.trip = foundTrip || null;

        if (this.trip) {
          console.log(`TripDetailPage: 找到行程: ${this.trip.title}`);
          this.itineraries = this.tripManager.getTripDetails(this.trip.id);
          this.quickActions = this.tripManager.getQuickActions();
          console.log(`TripDetailPage: 加载了 ${this.itineraries.length} 天行程`);
          console.log(`TripDetailPage: 加载了 ${this.quickActions.length} 个快速操作`);
        } else {
          console.error(`TripDetailPage: 未找到ID为 ${tripId} 的行程`);
        }
      }
    } else {
      console.error('TripDetailPage: 未获取到有效的路由参数');
    }
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理快速操作点击
  handleQuickActionClick = (action: QuickAction) => {
    console.log(`点击了快速操作: ${action.title}`);
    // 这里可以根据不同的操作类型进行相应的处理
  }

  // 处理活动点击
  handleActivityClick = (activity: Activity) => {
    console.log(`点击了活动: ${activity.title}`);
    // 这里可以跳转到活动详情页面或进行其他操作
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          IconText({
            iconText: IconConstants.BACK,
            fontSize: 18,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 18,
            iconHeight: 18
          })
        }
        .type(ButtonType.Circle)
        .backgroundColor(Color.Transparent)
        .onClick(this.handleBack)

        Blank()

        Text(this.trip?.destination || '行程详情')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)

        Blank()

        // 占位，保持标题居中
        Button()
          .type(ButtonType.Circle)
          .backgroundColor(Color.Transparent)
          .visibility(Visibility.Hidden)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(THEME_COLORS.cardBackground)

      if (this.trip) {
        Scroll() {
          Column() {
            // 行程卡片
            Column() {
              // 卡片头部
              Row() {
                Column() {
                  Text(this.trip.title)
                    .fontSize(20)
                    .fontWeight(600)
                    .fontColor(Color.White)
                    .alignSelf(ItemAlign.Start)
                    .maxLines(1)
                    .textOverflow({ overflow: TextOverflow.Ellipsis })

                  Text(this.trip.destination)
                    .fontSize(14)
                    .fontColor('rgba(255, 255, 255, 0.8)')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)

                // 状态标签
                Text(getStatusLabel(this.trip.status))
                  .fontSize(12)
                  .fontWeight(500)
                  .fontColor(Color.White)
                  .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                  .backgroundColor('rgba(255, 255, 255, 0.2)')
                  .borderRadius(12)
              }
              .width('100%')
              .alignItems(VerticalAlign.Top)
              .margin({ bottom: 16 })

              // 日期和进度信息
              Row() {
                Column() {
                  Text('出发日期')
                    .fontSize(12)
                    .fontColor('rgba(255, 255, 255, 0.7)')

                  Text(this.trip.startDate.replace(/-/g, '/'))
                    .fontSize(14)
                    .fontWeight(500)
                    .fontColor(Color.White)
                    .margin({ top: 2 })
                }
                .alignItems(HorizontalAlign.Start)

                Blank()

                Column() {
                  Text('天数')
                    .fontSize(12)
                    .fontColor('rgba(255, 255, 255, 0.7)')

                  Text(`${this.trip.daysCount}天`)
                    .fontSize(14)
                    .fontWeight(500)
                    .fontColor(Color.White)
                    .margin({ top: 2 })
                }
                .alignItems(HorizontalAlign.Center)

                Blank()

                Column() {
                  Text('进度')
                    .fontSize(12)
                    .fontColor('rgba(255, 255, 255, 0.7)')

                  Text(`${this.trip.progress}%`)
                    .fontSize(14)
                    .fontWeight(500)
                    .fontColor(Color.White)
                    .margin({ top: 2 })
                }
                .alignItems(HorizontalAlign.End)
              }
              .width('100%')
              .margin({ bottom: 12 })

              // 进度条
              Progress({ value: this.trip.progress, total: 100, type: ProgressType.Linear })
                .width('100%')
                .height(4)
                .color(Color.White)
                .backgroundColor('rgba(255, 255, 255, 0.3)')
            }
            .width('100%')
            .padding(20)
            .backgroundColor(THEME_COLORS.primary)
            .borderRadius(16)
            .margin({ left: 16, right: 16, top: 16, bottom: 16 })

            // 快速操作
            QuickActionGrid({
              quickActions: this.quickActions,
              onActionClick: this.handleQuickActionClick
            })

            // 行程安排
            ItineraryList({
              itineraries: this.itineraries,
              onActivityClick: this.handleActivityClick
            })
          }
        }
        .layoutWeight(1)
        .scrollBar(BarState.Off)
      } else {
        // 错误状态
        Column() {
          Text('行程信息加载失败')
            .fontSize(16)
            .fontColor(THEME_COLORS.textSecondary)
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(THEME_COLORS.background)
  }
}
